import React, { useCallback } from 'react';
import PropTypes from 'prop-types';
import { List as ListIcon, X as XIcon } from 'lucide-react';
import { useTreeState } from '@/components/features/navtree';
import { SortableNavTreeList } from '@/components/features/navtree/components/SortableNavTreeList';
import FindSelectedSectionButton from '@/components/features/navtree/components/FindSelectedSectionButton';

/**
 * Reusable TOC Panel component that can be used in both desktop sidebar and mobile overlay
 */
export const TOCPanel = ({
  isMobile = false,
  onClose = null,
  showHeader = true,
  className = ''
}) => {
  const { selectNode } = useTreeState();

  const handleNodeSelect = useCallback((node) => {
    if (!node) return;
    selectNode(node.nodeId);
    // Auto-close on mobile when a navigation item is selected
    if (isMobile && onClose) {
      onClose();
    }
  }, [selectNode, isMobile, onClose]);

  return (
    <div className={`h-full flex flex-col bg-gray-50 relative ${className}`}>
      {/* Panel Header */}
      {showHeader && (
        <div className="border-b bg-gray-50 flex items-center justify-between px-4 py-3 flex-shrink-0">
          <div className="flex items-center gap-2">
            <ListIcon size={16} className="text-gray-600" />
            <h3 className="font-medium text-gray-700">Table of Contents</h3>
          </div>
          {onClose && (
            <button
              onClick={onClose}
              className="p-1 hover:bg-gray-200 rounded transition-colors"
              aria-label="Close outline"
            >
              <XIcon size={16} className="text-gray-400 hover:text-gray-600" />
            </button>
          )}
        </div>
      )}

      {/* Scrollable Content */}
      <div className="flex-1 overflow-y-auto overflow-x-hidden scrollbar-thin left-panel-content">
        <SortableNavTreeList onSelect={handleNodeSelect} />
      </div>

      {/* Fixed Find Button */}
      <FindSelectedSectionButton />
    </div>
  );
};

TOCPanel.propTypes = {
  isMobile: PropTypes.bool,
  onClose: PropTypes.func,
  showHeader: PropTypes.bool,
  className: PropTypes.string
};
