'use client';

import React from 'react';
import { Info } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';

export const ContextTrigger = ({
  node,
  type = 'node',
  className = '',
  size = 16,
  children,
  isVisible = false
}) => {
  const { openSidebar } = useContextSidebar();

  const handleClick = (e) => {
    e.preventDefault();
    e.stopPropagation();

    // Calculate trigger position for caret positioning
    const rect = e.currentTarget.getBoundingClientRect();
    const position = {
      top: rect.top + rect.height / 2,
      right: rect.right
    };

    openSidebar(node, type, position);
  };

  return (
    <button
      onClick={handleClick}
      className={`
        context-trigger
        inline-flex items-center justify-center
        w-6 h-6 rounded-full
        bg-blue-50 hover:bg-blue-100
        border border-blue-200 hover:border-blue-300
        text-blue-600 hover:text-blue-700
        transition-all duration-300 ease-in-out
        ${isVisible ? 'opacity-100 scale-100' : 'opacity-0 scale-75'}
        focus:opacity-100 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1
        ${className}
      `}
      title="Open context sidebar"
      aria-label="Open context sidebar for this item"
    >
      {children || <Info size={size} />}
    </button>
  );
};
