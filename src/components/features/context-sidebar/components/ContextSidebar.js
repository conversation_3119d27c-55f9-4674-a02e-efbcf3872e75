'use client';

import React, { useEffect } from 'react';
import { X as XIcon } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';
import { SummarySection } from './SummarySection';
import { CaseLawSection } from './CaseLawSection';
import { NotesSection } from './NotesSection';
import './ContextSidebar.css';

export const ContextSidebar = () => {
  const {
    isOpen,
    currentNode,
    currentType,
    triggerPosition,
    closeSidebar
  } = useContextSidebar();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        closeSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, closeSidebar]);

  return (
    <div
      id="context-sidebar"
      className={isOpen ? 'sidebar-open' : 'sidebar-closed'}
    >
      {isOpen && currentNode && (
        <>
          {/* Chat bubble caret - positioned to point at info icon */}
          {triggerPosition && (
            <div
              className="context-sidebar-caret"
              style={{
                top: `${Math.max(16, Math.min(triggerPosition.top - 110, window.innerHeight - 160))}px`
              }}
            >
              <div className="context-sidebar-caret-arrow"></div>
            </div>
          )}

          {/* Fixed Header - similar to breadcrumb */}
          <div className="context-sidebar-header">
            <div className="context-sidebar-title">
              <h2>Context</h2>
            </div>
            <button
              onClick={closeSidebar}
              className="context-sidebar-close-button"
              aria-label="Close sidebar"
            >
              <XIcon size={16} />
            </button>
          </div>

          {/* Scrollable Content - similar to statute-content */}
          <div className="context-sidebar-content">
            <div className="context-sidebar-section">
              <SummarySection node={currentNode} type={currentType} />
            </div>
            <div className="context-sidebar-section">
              <CaseLawSection node={currentNode} type={currentType} />
            </div>
            <div className="context-sidebar-section">
              <NotesSection node={currentNode} type={currentType} />
            </div>
          </div>
        </>
      )}
    </div>
  );
};
