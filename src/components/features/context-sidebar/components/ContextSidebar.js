'use client';

import React, { useEffect } from 'react';
import { X as XIcon } from 'lucide-react';
import { useContextSidebar } from '../hooks/useContextSidebar';
import { SummarySection } from './SummarySection';
import { CaseLawSection } from './CaseLawSection';
import { NotesSection } from './NotesSection';

export const ContextSidebar = () => {
  const {
    isOpen,
    currentNode,
    currentType,
    triggerPosition,
    closeSidebar
  } = useContextSidebar();

  // Handle keyboard navigation
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (!isOpen) return;

      if (e.key === 'Escape') {
        closeSidebar();
      }
    };

    document.addEventListener('keydown', handleKeyDown);
    return () => document.removeEventListener('keydown', handleKeyDown);
  }, [isOpen, closeSidebar]);

  // Set CSS custom property for content width calculation
  useEffect(() => {
    const root = document.documentElement;
    if (isOpen) {
      root.style.setProperty('--context-sidebar-width', '384px'); // w-96 = 384px (no margin since we're using flex layout)
    } else {
      root.style.setProperty('--context-sidebar-width', '0px');
    }

    return () => {
      root.style.setProperty('--context-sidebar-width', '0px');
    };
  }, [isOpen]);

  if (!isOpen || !currentNode) {
    return null;
  }

  return (
    <div
      id="context-sidebar"
      className={`flex flex-col h-full bg-white border-l border-gray-200 shadow-lg transition-all duration-300 ease-in-out z-10 ${
        isOpen ? 'w-96 translate-x-0' : 'w-96 translate-x-full'
      }`}
    >
      {/* Chat bubble caret - positioned to point at info icon */}
      {isOpen && triggerPosition && (
        <div
          className="absolute left-0 transform -translate-x-2 z-10"
          style={{
            top: `${Math.max(16, Math.min(triggerPosition.top - 110, window.innerHeight - 160))}px` // Adjusted for new layout
          }}
        >
          <div className="w-0 h-0 border-t-8 border-b-8 border-r-8 border-t-transparent border-b-transparent border-r-gray-200"></div>
        </div>
      )}

      {/* Fixed Header - similar to breadcrumb */}
      <div className="flex-shrink-0 flex items-center justify-between p-4 border-b border-gray-200 bg-gray-50">
        <div className="flex-1 min-w-0">
          <h2 className="text-lg font-semibold text-gray-900 truncate">
            Context
          </h2>
        </div>
        <button
          onClick={closeSidebar}
          className="ml-2 p-1 rounded-md hover:bg-gray-200 transition-colors text-gray-700 hover:text-gray-900"
          aria-label="Close sidebar"
        >
          <XIcon size={16} className="text-gray-400 hover:text-gray-600" />
        </button>
      </div>

      {/* Scrollable Content - similar to statute-content */}
      <div className="flex-1 overflow-auto">
        <div className="border-b border-gray-200">
          <SummarySection node={currentNode} type={currentType} />
        </div>
        <div className="border-b border-gray-200">
          <CaseLawSection node={currentNode} type={currentType} />
        </div>
        <div>
          <NotesSection node={currentNode} type={currentType} />
        </div>
      </div>
    </div>
  );
};
