'use client';

import React, { useState, useEffect } from 'react';
import { Plus as PlusIcon, Edit3, Trash2, Save, X, ChevronDown, ChevronUp, AlertCircle, StickyNote } from 'lucide-react';
import { useSession } from 'next-auth/react';

export const NotesSection = ({ node, type }) => {
  const { data: session } = useSession();
  const [notes, setNotes] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState(null);
  const [isExpanded, setIsExpanded] = useState(true);
  const [isCreating, setIsCreating] = useState(false);
  const [editingNote, setEditingNote] = useState(null);
  const [newNoteText, setNewNoteText] = useState('');

  // Fetch notes for the current node
  useEffect(() => {
    if (!node?.nodeId || !session?.user?.id) return;

    const fetchNotes = async () => {
      setIsLoading(true);
      setError(null);

      try {
        const response = await fetch(`/api/v1/notes?nodeId=${encodeURIComponent(node.nodeId)}&type=${type}`);

        if (!response.ok) {
          throw new Error('Failed to fetch notes');
        }

        const data = await response.json();
        setNotes(data.notes || []);
      } catch (err) {
        console.error('Error fetching notes:', err);
        setError(err.message);
      } finally {
        setIsLoading(false);
      }
    };

    fetchNotes();
  }, [node?.nodeId, type, session?.user?.id]);

  const handleCreateNote = async () => {
    if (!newNoteText.trim() || !session?.user?.id) return;

    try {
      const response = await fetch('/api/v1/notes', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          nodeId: node.nodeId,
          type: type,
          text: newNoteText.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to create note');
      }

      const newNote = await response.json();
      setNotes(prev => [newNote, ...prev]);
      setNewNoteText('');
      setIsCreating(false);
    } catch (err) {
      console.error('Error creating note:', err);
      setError(err.message);
    }
  };

  const handleUpdateNote = async (noteId, text) => {
    if (!text.trim()) return;

    try {
      const response = await fetch(`/api/v1/notes/${noteId}`, {
        method: 'PUT',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          text: text.trim(),
        }),
      });

      if (!response.ok) {
        throw new Error('Failed to update note');
      }

      const updatedNote = await response.json();
      setNotes(prev => prev.map(note =>
        note.id === noteId ? updatedNote : note
      ));
      setEditingNote(null);
    } catch (err) {
      console.error('Error updating note:', err);
      setError(err.message);
    }
  };

  const handleDeleteNote = async (noteId) => {
    if (!confirm('Are you sure you want to delete this note?')) return;

    try {
      const response = await fetch(`/api/v1/notes/${noteId}`, {
        method: 'DELETE',
      });

      if (!response.ok) {
        throw new Error('Failed to delete note');
      }

      setNotes(prev => prev.filter(note => note.id !== noteId));
    } catch (err) {
      console.error('Error deleting note:', err);
      setError(err.message);
    }
  };

  const formatDate = (dateString) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        month: 'short',
        day: 'numeric',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch {
      return 'Unknown date';
    }
  };

  if (!session?.user?.id) {
    return (
      <div className="p-4">
        <div className="text-center py-8">
          <StickyNote size={48} className="text-gray-300 mx-auto mb-4" />
          <p className="text-gray-500 text-sm">Please sign in to view and create notes.</p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-4">
      {/* Section Header */}
      <div className="flex items-center justify-between mb-4">
        <button
          onClick={() => setIsExpanded(!isExpanded)}
          className="flex items-center gap-2 text-lg font-semibold text-gray-900 hover:text-gray-700"
        >
          {isExpanded ? <ChevronUp size={20} /> : <ChevronDown size={20} />}
          Notes
        </button>
        <div className="flex items-center gap-2">
          {notes.length > 0 && (
            <span className="text-sm text-gray-600 bg-gray-100 px-2 py-1 rounded-full">
              {notes.length}
            </span>
          )}
          <button
            onClick={() => setIsCreating(true)}
            className="p-1 rounded-md hover:bg-gray-100 transition-colors"
            title="Add note"
          >
            <PlusIcon size={16} className="text-gray-400 hover:text-gray-600" />
          </button>
        </div>
      </div>

      {/* Content */}
      {isExpanded && (
        <div className="space-y-4">
          {error && (
            <div className="flex items-start gap-3 p-3 bg-red-50 border border-red-200 rounded-md">
              <AlertCircle size={20} className="text-red-600 flex-shrink-0 mt-0.5" />
              <div>
                <p className="text-sm font-medium text-red-800">Error with notes</p>
                <p className="text-sm text-red-600 mt-1">{error}</p>
              </div>
            </div>
          )}

          {/* Create new note */}
          {isCreating && (
            <div className="border border-gray-200 rounded-md p-3 bg-blue-50">
              <textarea
                value={newNoteText}
                onChange={(e) => setNewNoteText(e.target.value)}
                placeholder="Add your note..."
                className="w-full p-2 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                rows={3}
                autoFocus
              />
              <div className="flex justify-end gap-2 mt-2">
                <button
                  onClick={() => {
                    setIsCreating(false);
                    setNewNoteText('');
                  }}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
                >
                  Cancel
                </button>
                <button
                  onClick={handleCreateNote}
                  disabled={!newNoteText.trim()}
                  className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Save
                </button>
              </div>
            </div>
          )}

          {/* Notes list */}
          {isLoading ? (
            <div className="text-center py-4">
              <p className="text-sm text-gray-600">Loading notes...</p>
            </div>
          ) : notes.length > 0 ? (
            <div className="space-y-3">
              {notes.map((note) => (
                <div key={note.id} className="border border-gray-200 rounded-md p-3 bg-white">
                  {editingNote === note.id ? (
                    <EditNoteForm
                      note={note}
                      onSave={(text) => handleUpdateNote(note.id, text)}
                      onCancel={() => setEditingNote(null)}
                    />
                  ) : (
                    <div>
                      <div className="flex items-start justify-between mb-2">
                        <div className="text-xs text-gray-500">
                          {formatDate(note.createdAt)}
                          {note.updatedAt !== note.createdAt && ' (edited)'}
                        </div>
                        <div className="flex gap-1">
                          <button
                            onClick={() => setEditingNote(note.id)}
                            className="p-1 rounded hover:bg-gray-100 transition-colors"
                            title="Edit note"
                          >
                            <Edit3 size={14} />
                          </button>
                          <button
                            onClick={() => handleDeleteNote(note.id)}
                            className="p-1 rounded hover:bg-gray-100 text-red-600 transition-colors"
                            title="Delete note"
                          >
                            <Trash2 size={14} />
                          </button>
                        </div>
                      </div>
                      <p className="text-sm text-gray-700 whitespace-pre-wrap">{note.text}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          ) : (
            <div className="text-center py-8">
              <StickyNote size={48} className="text-gray-300 mx-auto mb-4" />
              <p className="text-gray-500 text-sm mb-2">No notes for this section yet.</p>
              <button
                onClick={() => setIsCreating(true)}
                className="text-blue-600 hover:text-blue-700 text-sm underline"
              >
                Add your first note
              </button>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

// Helper component for editing notes
const EditNoteForm = ({ note, onSave, onCancel }) => {
  const [text, setText] = useState(note.text);

  return (
    <div>
      <textarea
        value={text}
        onChange={(e) => setText(e.target.value)}
        className="w-full p-2 border border-gray-300 rounded-md resize-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        rows={3}
        autoFocus
      />
      <div className="flex justify-end gap-2 mt-2">
        <button
          onClick={onCancel}
          className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800"
        >
          Cancel
        </button>
        <button
          onClick={() => onSave(text)}
          disabled={!text.trim()}
          className="px-3 py-1 text-sm bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Save
        </button>
      </div>
    </div>
  );
};
