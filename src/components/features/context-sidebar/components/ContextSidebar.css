/* ContextSidebar Component Styles */

#context-sidebar {
  /* Base layout */
  display: flex;
  flex-direction: column;
  height: 100%;
  background-color: white;
  border-left: 1px solid #d1d5db; /* border-gray-200 */
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05); /* shadow-lg */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); /* transition-all duration-300 ease-in-out */
}

/* Sidebar states */
#context-sidebar.sidebar-open {
  width: 24rem; /* w-96 = 384px */
}

#context-sidebar.sidebar-closed {
  width: 0;
  overflow: hidden;
}

/* Chat bubble caret */
.context-sidebar-caret {
  position: absolute;
  left: 0;
  transform: translateX(-0.5rem); /* -translate-x-2 */
  z-index: 10;
}

.context-sidebar-caret-arrow {
  width: 0;
  height: 0;
  border-top: 8px solid transparent;
  border-bottom: 8px solid transparent;
  border-right: 8px solid white;
}

/* Header section */
.context-sidebar-header {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem; /* p-4 */
  border-bottom: 1px solid #d1d5db; /* border-gray-200 */
  background-color: #f9fafb; /* bg-gray-50 */
}

.context-sidebar-title {
  flex: 1;
  min-width: 0;
}

.context-sidebar-title h2 {
  font-size: 1.125rem; /* text-lg */
  font-weight: 600; /* font-semibold */
  color: #111827; /* text-gray-900 */
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; /* truncate */
}

.context-sidebar-close-button {
  margin-left: 0.5rem; /* ml-2 */
  padding: 0.25rem; /* p-1 */
  border-radius: 0.375rem; /* rounded-md */
  transition: colors 0.15s ease-in-out;
  color: #374151; /* text-gray-700 */
  background: transparent;
  border: none;
  cursor: pointer;
}

.context-sidebar-close-button:hover {
  background-color: #e5e7eb; /* hover:bg-gray-200 */
  color: #111827; /* hover:text-gray-900 */
}

.context-sidebar-close-button svg {
  color: #9ca3af; /* text-gray-400 */
}

.context-sidebar-close-button:hover svg {
  color: #4b5563; /* hover:text-gray-600 */
}

/* Content section */
.context-sidebar-content {
  flex: 1;
  overflow: auto;
}

.context-sidebar-section {
  border-bottom: 1px solid #d1d5db; /* border-gray-200 */
}

.context-sidebar-section:last-child {
  border-bottom: none;
}
