import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { ContextTrigger } from './ContextTrigger';
import { ContextSidebarProvider } from '../context/ContextSidebarContext';
import { useContextSidebar } from '../hooks/useContextSidebar';

const mockNode = {
  nodeId: '/test/node/1',
  text: 'Test Node Text',
  type: 'section'
};

const TestWrapper = ({ children }) => {
  return (
    <ContextSidebarProvider>
      {children}
    </ContextSidebarProvider>
  );
};

describe('ContextTrigger', () => {
  test('renders trigger button with default icon when visible', () => {
    render(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button', { name: /open context sidebar/i });
    expect(button).toBeInTheDocument();
    expect(button).toHaveAttribute('title', 'Open context sidebar');
  });

  test('renders with custom children instead of default icon', () => {
    render(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={true}>
          <span data-testid="custom-icon">Custom Icon</span>
        </ContextTrigger>
      </TestWrapper>
    );

    expect(screen.getByTestId('custom-icon')).toBeInTheDocument();
    expect(screen.getByText('Custom Icon')).toBeInTheDocument();
  });

  test('applies custom className', () => {
    render(
      <TestWrapper>
        <ContextTrigger node={mockNode} className="custom-class" isVisible={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveClass('custom-class');
  });

  test('opens sidebar when clicked', () => {
    const TestComponent = () => {
      const { isOpen, currentNode } = useContextSidebar();

      return (
        <div>
          <ContextTrigger node={mockNode} type="node" isVisible={true} />
          <div data-testid="sidebar-state">
            {isOpen ? `open: ${currentNode?.nodeId}` : 'closed'}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    expect(screen.getByTestId('sidebar-state')).toHaveTextContent('closed');

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(screen.getByTestId('sidebar-state')).toHaveTextContent(`open: ${mockNode.nodeId}`);
  });

  test('passes correct type to openSidebar', () => {
    const TestComponent = () => {
      const { isOpen, currentType } = useContextSidebar();

      return (
        <div>
          <ContextTrigger node={mockNode} type="highlight" isVisible={true} />
          <div data-testid="sidebar-type">
            {isOpen ? `type: ${currentType}` : 'closed'}
          </div>
        </div>
      );
    };

    render(
      <TestWrapper>
        <TestComponent />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    fireEvent.click(button);

    expect(screen.getByTestId('sidebar-type')).toHaveTextContent('type: highlight');
  });

  test('prevents event propagation and default behavior', () => {
    const mockParentClick = jest.fn();
    const mockPreventDefault = jest.fn();
    const mockStopPropagation = jest.fn();

    render(
      <TestWrapper>
        <div onClick={mockParentClick}>
          <ContextTrigger node={mockNode} isVisible={true} />
        </div>
      </TestWrapper>
    );

    const button = screen.getByRole('button');

    // Create a mock event
    const mockEvent = {
      preventDefault: mockPreventDefault,
      stopPropagation: mockStopPropagation
    };

    // Manually trigger the click handler
    fireEvent.click(button, mockEvent);

    // The parent click should not be called due to stopPropagation
    expect(mockParentClick).not.toHaveBeenCalled();
  });

  test('has proper accessibility attributes', () => {
    render(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');
    expect(button).toHaveAttribute('aria-label', 'Open context sidebar for this item');
    expect(button).toHaveAttribute('title', 'Open context sidebar');
  });

  test('has proper hover and focus styles', () => {
    render(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={true} />
      </TestWrapper>
    );

    const button = screen.getByRole('button');

    // Check that it has the expected CSS classes for styling
    expect(button).toHaveClass('context-trigger');
    expect(button).toHaveClass('hover:bg-blue-100');
    expect(button).toHaveClass('focus:ring-2');
    expect(button).toHaveClass('focus:ring-blue-500');
  });

  test('shows/hides based on isVisible prop', () => {
    const { rerender } = render(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={false} />
      </TestWrapper>
    );

    // Should render but be hidden when not visible
    const button = screen.getByRole('button');
    expect(button).toBeInTheDocument();
    expect(button).toHaveClass('opacity-0');

    // Should be visible when isVisible is true
    rerender(
      <TestWrapper>
        <ContextTrigger node={mockNode} isVisible={true} />
      </TestWrapper>
    );

    expect(button).toHaveClass('opacity-100');
  });
});
