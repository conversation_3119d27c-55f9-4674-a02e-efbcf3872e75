'use client';

import React, { createContext, useState, useCallback } from 'react';

export const ContextSidebarContext = createContext({
  isOpen: false,
  currentNode: null,
  currentType: null, // 'node', 'highlight', 'reference'
  activeSection: 'summary', // 'summary', 'caselaw', 'notes'
  triggerPosition: null, // { top: number, right: number } for caret positioning
  openSidebar: () => {},
  closeSidebar: () => {},
  setActiveSection: () => {},
  sidebarWidth: 400,
  setSidebarWidth: () => {}
});

export function ContextSidebarProvider({ children }) {
  const [isOpen, setIsOpen] = useState(false);
  const [currentNode, setCurrentNode] = useState(null);
  const [currentType, setCurrentType] = useState(null);
  const [activeSection, setActiveSection] = useState('summary');
  const [sidebarWidth, setSidebarWidth] = useState(400);
  const [triggerPosition, setTriggerPosition] = useState(null);

  const openSidebar = useCallback((node, type = 'node', position = null) => {
    setCurrentNode(node);
    setCurrentType(type);
    setTriggerPosition(position);
    setIsOpen(true);
    // Default to summary section when opening
    setActiveSection('summary');
  }, []);

  const closeSidebar = useCallback(() => {
    setIsOpen(false);
    setTriggerPosition(null);
    // Keep current node data for potential re-opening
  }, []);

  const value = {
    isOpen,
    currentNode,
    currentType,
    activeSection,
    triggerPosition,
    openSidebar,
    closeSidebar,
    setActiveSection,
    sidebarWidth,
    setSidebarWidth
  };

  return (
    <ContextSidebarContext.Provider value={value}>
      {children}
    </ContextSidebarContext.Provider>
  );
}

// Note: useContextSidebar is exported from hooks/useContextSidebar.js
