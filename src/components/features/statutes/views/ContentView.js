import { memo, useCallback, useState, useEffect, useRef } from 'react';
import PropTypes from 'prop-types';
import { PageTitle } from '@/components/features/shared';
import { BreadcrumbPath } from '@/components/features/breadcrumb';
import { FooterNavigation } from '../components/FooterNavigation';
import { getPageTitle } from '../utils';
import { StatuteNodeRenderer } from '../components/StatuteNodeRenderer';
import { useTreeState } from '@/components/features/navtree';
import { ContentViewFactory } from '../factories/ContentViewFactory';
import { useLayout } from '@/app/LayoutContext';
import { ContextSidebar } from '@/components/features/context-sidebar';
import './ContentView.css';

// Helper component to recursively render nodes
const RenderNodes = memo(({ nodes, selectedNodeId, onLocateNode }) => {
  if (!nodes || !Array.isArray(nodes)) return null;

  return nodes.map((node, index) => (
    <div key={`${node.nodeId}-${index}`}>
      <StatuteNodeRenderer
        node={node}
        isSelected={node.nodeId === selectedNodeId}
        onLocateNode={onLocateNode}
        selectedNodeId={selectedNodeId}
      />
      {/* Recursively render children if they exist */}
      {node.children && node.type !== 'section' && (
        <RenderNodes
          nodes={node.children}
          selectedNodeId={selectedNodeId}
          onLocateNode={onLocateNode}
        />
      )}
    </div>
  ));
});

RenderNodes.displayName = 'RenderNodes';

RenderNodes.propTypes = {
  nodes: PropTypes.arrayOf(PropTypes.object),
  selectedNodeId: PropTypes.string,
  onLocateNode: PropTypes.func.isRequired
};

export const ContentView = memo(({ selectedNode, content }) => {
  const { selectNode } = useTreeState();
  const { isMobile } = useLayout();
  const contentRef = useRef(null);
  const [isFooterFixed, setIsFooterFixed] = useState(true);
  const [isCalculated, setIsCalculated] = useState(false);

  const handleLocateNode = useCallback((node) => {
    if (node && node.nodeId) {
      selectNode(node.nodeId);
    }
  }, [selectNode]);

  // Calculate if content overflows and determine footer positioning
  useEffect(() => {
    // Reset calculation state when content changes
    setIsCalculated(false);

    if (!contentRef.current) return;

    const checkContentOverflow = () => {
      const contentElement = contentRef.current;

      // Always set calculated to true, even if contentElement is null
      if (!contentElement) {
        setIsCalculated(true);
        return;
      }

      // On mobile, footer should always be fixed
      if (isMobile) {
        setIsFooterFixed(true);
        setIsCalculated(true);
        return;
      }

      const viewportHeight = window.innerHeight;
      const breadcrumbHeight = 50; // Height of breadcrumb bar
      const footerHeight = 60; // Approximate height of footer navigation
      const availableContentHeight = viewportHeight - breadcrumbHeight - footerHeight;

      // Check if content height exceeds available space
      const contentHeight = contentElement.scrollHeight;
      const shouldBeFixed = contentHeight > availableContentHeight;

      setIsFooterFixed(shouldBeFixed);
      setIsCalculated(true);
    };

    // Track all timeouts and cleanup functions
    const timeouts = [];
    let rafId;

    // Wait for content to be fully rendered before checking
    rafId = requestAnimationFrame(() => {
      const timeout = setTimeout(checkContentOverflow, 100);
      timeouts.push(timeout);
    });

    // Also check after a longer delay to catch any slow-loading content
    const secondaryTimeout = setTimeout(checkContentOverflow, 500);
    timeouts.push(secondaryTimeout);

    // Check on window resize
    window.addEventListener('resize', checkContentOverflow);

    return () => {
      window.removeEventListener('resize', checkContentOverflow);
      if (rafId) cancelAnimationFrame(rafId);
      timeouts.forEach(timeout => clearTimeout(timeout));
    };
  }, [content, selectedNode, isMobile]);

  // Create current node object for FooterNavigation
  const currentNode = selectedNode ? { nodeId: selectedNode.nodeId } : null;

  // Get the specific view content based on node type using ContentViewFactory
  const getViewContent = () => {
    if (!selectedNode || !content) return null;

    // Try to get a specific view component from the factory
    const specificView = ContentViewFactory.getContentComponent(selectedNode);
    if (specificView) {
      const { Component, props } = specificView;
      return <Component {...props} />;
    }

    // Default content view with recursive node rendering
    return (
      <RenderNodes
        nodes={content}
        selectedNodeId={selectedNode.nodeId}
        onLocateNode={handleLocateNode}
      />
    );
  };

  return (
    <>
      <PageTitle title={getPageTitle(selectedNode)} />
      <div className="h-full flex flex-col" style={{ height: 'calc(100vh - 60px)' }}>
        {/* Fixed Breadcrumb */}
        <div className="flex-shrink-0">
          <BreadcrumbPath content={selectedNode} contentRef={contentRef} />
        </div>

        {/* Main Content Area - Single responsive container */}
        <div className="flex-1 flex h-full overflow-hidden relative">
          {/* Content Container - Responsive width based on sidebar state */}
          <div id="content-container" className="flex flex-col h-full overflow-hidden transition-all duration-300 ease-in-out"
               style={{ width: 'calc(100% - var(--context-sidebar-width, 0px))' }}>
            {/* Scrollable Content */}
            <div
              ref={contentRef}
              className={`flex-1 overflow-auto scrollbar-thin statute-content ${!isFooterFixed ? 'content-view--inline' : ''}`}
              style={{
                paddingBottom: !isFooterFixed ? '60px' : '0'
              }}
            >
              {getViewContent()}

              {/* Inline Footer Navigation - rendered inside content when not fixed */}
              {!isFooterFixed && currentNode && isCalculated && (
                <FooterNavigation
                  currentNode={currentNode}
                  isFixed={false}
                />
              )}
            </div>

            {/* Fixed Footer Navigation - rendered outside content when fixed */}
            {isFooterFixed && isCalculated && (
              <div className="flex-shrink-0">
                {currentNode && (
                  <FooterNavigation
                    currentNode={currentNode}
                    isFixed={true}
                  />
                )}
              </div>
            )}
          </div>

          {/* Context Sidebar - Absolute positioned to slide in from right */}
          <ContextSidebar />
        </div>
      </div>
    </>
  );
});

ContentView.propTypes = {
  selectedNode: PropTypes.object.isRequired,
  content: PropTypes.arrayOf(PropTypes.object).isRequired
};

ContentView.displayName = 'ContentView';
