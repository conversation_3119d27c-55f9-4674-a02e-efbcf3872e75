/* ContentView footer positioning modes */

/* Prevent flash during calculation by hiding footer initially */
.footer-navigation {
  transition: opacity 0.1s ease-in-out;
}

/* Reduce bottom margin on last section when footer is inline */
.content-view--inline .section:last-child {
  margin-bottom: 0.5rem;
}

/* Add right margin to content to make space for gutter icons */
.statute-content {
  position: relative;
}

/* Context trigger positioning */
.context-trigger-container {
  transition: opacity 0.3s ease-in-out;
}
