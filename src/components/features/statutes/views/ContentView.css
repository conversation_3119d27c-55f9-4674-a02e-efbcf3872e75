/* ContentView footer positioning modes */

/* Prevent flash during calculation by hiding footer initially */
.footer-navigation {
  transition: opacity 0.2s ease-in-out;
}

/* Reduce bottom margin on last section when footer is inline */
.content-view--inline .section:last-child {
  margin-bottom: 0.5rem;
}

/* Context trigger positioning - consistent horizontal alignment */
.context-trigger-container {
  transition: opacity 0.3s ease-in-out;
  position: absolute;
  right: 0.5rem; /* Fixed distance from content right edge */
  z-index: 10;
}

/* Ensure all relative containers allow absolute positioning */
.section-header,
.section-title,
.section-text {
  position: relative;
}
