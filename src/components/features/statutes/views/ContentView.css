/* ContentView footer positioning modes */

/* Prevent flash during calculation by hiding footer initially */
.footer-navigation {
  transition: opacity 0.1s ease-in-out;
}

/* Reduce bottom margin on last section when footer is inline */
.content-view--inline .section:last-child {
  margin-bottom: 0.5rem;
}

/* Add right margin to content to make space for gutter icons */
.statute-content {
  position: relative;
}

/* Context trigger visibility - only show on direct hover */
.context-trigger-container {
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

/* Show context trigger only when the direct parent is hovered */
.section:hover > .section-title > .context-trigger-container,
.section:hover > .section-header > .context-trigger-container,
.statute-section:hover > .section-text > .context-trigger-container,
.code:hover > .section-header > .context-trigger-container,
.title:hover > .section-header > .context-trigger-container,
.subtitle:hover > .section-header > .context-trigger-container,
.chapter:hover > .section-header > .context-trigger-container,
.subchapter:hover > .section-header > .context-trigger-container {
  opacity: 1;
  pointer-events: auto;
}
