import React, { useState } from 'react';
import PropTypes from 'prop-types';
import { Locate as LocateIcon } from 'lucide-react';
import { BookmarkButton } from '@/components/features/bookmarks';
import { LocateNodeButton } from '@/components/features/breadcrumb';
import { HighlightContent } from '@/components/features/highlights';
import { ContextTrigger } from '@/components/features/context-sidebar';
import '@/components/StatuteBrowser.css';
import '@/components/features/breadcrumb/components/LocateNodeButton.css';

// Helper to determine header tag
const getHeaderTag = (type) => {
  switch (type) {
    case 'collection':
      return { tag: 'h4', locateMargin: 'mt-1' };
    case 'code':
      return { tag: 'h1', locateMargin: 'mt-5' };
    case 'title':
    case 'article':
      return { tag: 'h2', locateMargin: 'mt-4' };
    case 'subtitle':
    case 'subarticle':
      return { tag: 'h2', locateMargin: 'mt-3.5' };
    case 'chapter':
    case 'subchapter':
      return { tag: 'h3', locateMargin: 'mt-2.5' };
    case 'section':
      return { tag: 'h4', locateMargin: 'mt-3' };
    default:
      return { tag: 'h4', locateMargin: 'mt-3' };
  }
};

export const StatuteNodeRenderer = ({
  node,
  isSelected,
  onLocateNode,
  selectedNodeId
}) => {
  const [isHovered, setIsHovered] = useState(false);

  const isRepealed = (text) => {
    return text.includes('Repealed by');
  };

  // Skip rendering for subsections - they're handled recursively inside sections
  if (node.isSubsection()) {
    return null;
  }

  // Handle header sections (code, title, subtitle, chapter, subchapter)
  if (!(node.isSection())) {
    const { tag: HeaderTag, locateMargin } = getHeaderTag(node.type);
    return (
      <div
        className={`${node.type} ${isSelected ? 'selected-section' : ''} group`}
        data-node-id={node.nodeId}
        onMouseEnter={(e) => {
          e.stopPropagation();
          setIsHovered(true);
        }}
        onMouseLeave={(e) => {
          e.stopPropagation();
          setIsHovered(false);
        }}
      >
        <div className="section-header relative flex gap-2">
          <BookmarkButton node={node} />
          <HeaderTag className="flex-1">
            <HighlightContent node={node}>
              {node.text.toUpperCase()}
            </HighlightContent>
          </HeaderTag>
          <span className="absolute" style={{
            right: '-2rem',
            top: locateMargin.includes('mt-') ?
              (parseFloat(locateMargin.replace('mt-', '')) * 0.25) + 'rem' :
              '0'
          }}>
            <ContextTrigger node={node} type="node" isVisible={isHovered} />
          </span>
          <LocateNodeButton node={node} onLocate={() => onLocateNode(node)} icon={LocateIcon} className={locateMargin} />
        </div>
      </div>
    );
  }

  // Create a separate component for subsections to handle hover state
  const SubsectionRenderer = ({ subsection, index }) => {
    const [subsectionHovered, setSubsectionHovered] = useState(false);

    return (
      <div
        key={`${subsection.nodeId}-${index}`}
        className={`statute-section${isRepealed(subsection.text) ? ' repealed' : ''} group`}
        data-node-id={subsection.nodeId}
        id={subsection.getSubsectionPath?.()}
        onMouseEnter={(e) => {
          e.stopPropagation();
          setSubsectionHovered(true);
        }}
        onMouseLeave={(e) => {
          e.stopPropagation();
          setSubsectionHovered(false);
        }}
      >
        <div className={`section-text subsection-level-${subsection.level} highlight-content relative`}>
          <HighlightContent node={subsection}>
            {subsection.text}
          </HighlightContent>
          <div className="absolute" style={{ right: '-2rem', top: '0.25rem' }}>
            <ContextTrigger
              node={subsection}
              type="node"
              size={14}
              isVisible={subsectionHovered}
            />
          </div>
        </div>
        {subsection.children?.length > 0 && (
          <div className="highlight-content">
            {renderSubsections(subsection.children)}
          </div>
        )}
      </div>
    );
  };

  const renderSubsections = (subsections) => {
    return subsections.map((subsection, index) => (
      <SubsectionRenderer
        key={`${subsection.nodeId}-${index}`}
        subsection={subsection}
        index={index}
      />
    ));
  };

  return (
    <div
      className={`section ${isSelected ? 'selected-section' : ''} group`}
      data-node-id={node.nodeId}
      onMouseEnter={(e) => {
        e.stopPropagation();
        setIsHovered(true);
      }}
      onMouseLeave={(e) => {
        e.stopPropagation();
        setIsHovered(false);
      }}
    >
      <div className="section-title flex items-start gap-2 relative">
        <span className="flex-shrink-0 self-start mt-2.5">
          <BookmarkButton node={node} />
        </span>
        <h4 className="flex-1">
          <HighlightContent node={node}>
            {node.text}
          </HighlightContent>
        </h4>
        <span className="flex-shrink-0 self-start absolute" style={{ right: '-2rem', top: '0.75rem' }}>
          <ContextTrigger node={node} type="node" isVisible={isHovered} />
        </span>
        <span className="flex-shrink-0 self-start">
          <LocateNodeButton node={node} onLocate={() => onLocateNode(node)} icon={LocateIcon} className="mt-3" />
        </span>
      </div>
      <div className="section-content">
        {node.children?.length > 0 && renderSubsections(node.children)}
      </div>
      {node.amendment_history?.length > 0 && (
        <div className="amendment-history mt-4 text-sm text-gray-600">
          {node.amendment_history.map((amendment, index) => (
            <p key={index} className="amendment-entry">
              {amendment}
            </p>
          ))}
        </div>
      )}
    </div>
  );
};

StatuteNodeRenderer.propTypes = {
  node: PropTypes.object.isRequired,
  isSelected: PropTypes.bool,
  onLocateNode: PropTypes.func.isRequired,
  selectedNodeId: PropTypes.string
};

StatuteNodeRenderer.displayName = 'StatuteNodeRenderer';
